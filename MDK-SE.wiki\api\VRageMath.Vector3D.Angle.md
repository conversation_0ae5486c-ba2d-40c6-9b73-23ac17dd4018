← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public static double Angle(Vector3D a, Vector3D b)
```

Gets angle between 2 vectors in radians

### Returns

[double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6)

Angle in radians

### Parameters

* [Vector3D](VRageMath.Vector3D) a
* [Vector3D](VRageMath.Vector3D) b
