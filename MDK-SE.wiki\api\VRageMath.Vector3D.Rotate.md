← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public static void Rotate(ref Vector3D vector, ref MatrixD rotationMatrix, out Vector3D result)
```

### Parameters

* [Vector3D](VRageMath.Vector3D) vector
* [MatrixD](VRageMath.MatrixD) rotationMatrix
* [Vector3D](VRageMath.Vector3D) result
### Summary

```csharp
public static Vector3D Rotate(Vector3D vector, MatrixD rotationMatrix)
```

### Returns

[Vector3D](VRageMath.Vector3D)

### Parameters

* [Vector3D](VRageMath.Vector3D) vector
* [MatrixD](VRageMath.MatrixD) rotationMatrix
### Summary

```csharp
public Vector3D Rotate(Vector3D axis, double rotationInRadians)
```

### Returns

[Vector3D](VRageMath.Vector3D)

### Parameters

* [Vector3D](VRageMath.Vector3D) axis
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) rotationInRadians
