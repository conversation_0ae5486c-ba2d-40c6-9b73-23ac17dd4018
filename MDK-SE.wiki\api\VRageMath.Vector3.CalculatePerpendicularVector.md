← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3](VRageMath.Vector3)

### Summary

```csharp
public static Vector3 CalculatePerpendicularVector(Vector3 v)
```

### Returns

[Vector3](VRageMath.Vector3)

### Parameters

* [Vector3](VRageMath.Vector3) v
### Summary

```csharp
public void CalculatePerpendicularVector(out Vector3 result)
```

### Parameters

* [Vector3](VRageMath.Vector3) result
