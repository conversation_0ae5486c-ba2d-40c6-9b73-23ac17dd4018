← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3B](VRageMath.Vector3B)

### Summary

```csharp
public static Vector3B Fit(Vector3 vec, float range)
```

Puts Vector3 into Vector3B, value -127 represents -range, 128 represents range

### Returns

[Vector3B](VRageMath.Vector3B)

### Parameters

* [Vector3](VRageMath.Vector3) vec
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) range
