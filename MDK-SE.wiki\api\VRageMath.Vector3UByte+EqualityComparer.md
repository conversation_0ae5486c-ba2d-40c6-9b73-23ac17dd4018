← [Index](Api-Index) ← [Namespace Index](Namespace-Index)

#### EqualityComparer Class

```csharp
public class EqualityComparer: IEqualityComparer<Vector3UByte>, IComparer<Vector3UByte>
```

**Namespace:** [VRageMath](VRageMath)  
**Assembly:** VRage.Math.dll

**Implements:**  
* [IComparer&lt;Vector3UByte&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IComparer-1?view=netframework-4.6)  
* [IEqualityComparer&lt;Vector3UByte&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEqualityComparer-1?view=netframework-4.6)

