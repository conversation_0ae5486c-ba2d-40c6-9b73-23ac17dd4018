← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3](VRageMath.Vector3)

### Summary

```csharp
public static bool ArePerpendicular(ref Vector3 a, ref Vector3 b)
```

### Returns

[bool](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean?view=netframework-4.6)

### Parameters

* [Vector3](VRageMath.Vector3) a
* [Vector3](VRageMath.Vector3) b
### Summary

```csharp
public static bool ArePerpendicular(Vector3 a, Vector3 b)
```

### Returns

[bool](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean?view=netframework-4.6)

### Parameters

* [Vector3](VRageMath.Vector3) a
* [Vector3](VRageMath.Vector3) b
