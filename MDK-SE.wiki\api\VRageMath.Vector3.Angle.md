← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3](VRageMath.Vector3)

### Summary

```csharp
public static double Angle(Vector3 a, Vector3 b)
```

Gets angle between 2 vectors in radians

### Returns

[double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6)

Angle in radians

### Parameters

* [Vector3](VRageMath.Vector3) a
* [Vector3](VRageMath.Vector3) b
