← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector4UByte](VRageMath.Vector4UByte)

### Summary

```csharp
public Vector4UByte(byte x, byte y, byte z, byte w)
```

### Parameters

* [byte](https://docs.microsoft.com/en-us/dotnet/api/System.Byte?view=netframework-4.6) x
* [byte](https://docs.microsoft.com/en-us/dotnet/api/System.Byte?view=netframework-4.6) y
* [byte](https://docs.microsoft.com/en-us/dotnet/api/System.Byte?view=netframework-4.6) z
* [byte](https://docs.microsoft.com/en-us/dotnet/api/System.Byte?view=netframework-4.6) w
