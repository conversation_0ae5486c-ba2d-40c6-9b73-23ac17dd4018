← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector4](VRageMath.Vector4)

### Summary

```csharp
public static Vector4 Min(Vector4 value1, Vector4 value2)
```

Returns a vector that contains the lowest value from each matching pair of components.

### Returns

[Vector4](VRageMath.Vector4)

### Parameters

* [Vector4](VRageMath.Vector4) value1
* [Vector4](VRageMath.Vector4) value2
### Summary

```csharp
public static void Min(ref Vector4 value1, ref Vector4 value2, out Vector4 result)
```

Returns a vector that contains the lowest value from each matching pair of components.

### Parameters

* [Vector4](VRageMath.Vector4) value1
* [Vector4](VRageMath.Vector4) value2
* [Vector4](VRageMath.Vector4) result
