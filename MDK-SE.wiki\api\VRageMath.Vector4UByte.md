← [Index](Api-Index) ← [Namespace Index](Namespace-Index)

#### Vector4UByte Struct

```csharp
public struct Vector4UByte
```

**Namespace:** [VRageMath](VRageMath)  
**Assembly:** VRage.Math.dll

#### Fields

[byte W](VRageMath.Vector4UByte.W)

> 

[byte X](VRageMath.Vector4UByte.X)

> 

[byte Y](VRageMath.Vector4UByte.Y)

> 

[byte Z](VRageMath.Vector4UByte.Z)

> 

#### Properties

[byte Item { get; set; }](VRageMath.Vector4UByte.Item)

> 

#### Constructors

[Vector4UByte(byte x, byte y, byte z, byte w)](VRageMath.Vector4UByte..ctor)

> 

#### Methods

[static Vector4UByte Normalize(Vector3 vec, float range)](VRageMath.Vector4UByte.Normalize)

> Normalizes Vector3 into Vector4UByte, scales vector from (-range, range) to (0, 255)

[static Vector4UByte Round(Vector3 vec)](VRageMath.Vector4UByte.Round)

> 

[static Vector4UByte Round(Vector4 vec)](VRageMath.Vector4UByte.Round)

> 

[string ToString()](VRageMath.Vector4UByte.ToString)

> 

