← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3](VRageMath.Vector3)

### Summary

```csharp
public static Vector3 Lerp(Vector3 value1, Vector3 value2, float amount)
```

Performs a linear interpolation between two vectors.

### Returns

[Vector3](VRageMath.Vector3)

### Parameters

* [Vector3](VRageMath.Vector3) value1
* [Vector3](VRageMath.Vector3) value2
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) amount
### Summary

```csharp
public static void Lerp(ref Vector3 value1, ref Vector3 value2, float amount, out Vector3 result)
```

Performs a linear interpolation between two vectors.

### Parameters

* [Vector3](VRageMath.Vector3) value1
* [Vector3](VRageMath.Vector3) value2
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) amount
* [Vector3](VRageMath.Vector3) result
