← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3](VRageMath.Vector3)

### Summary

```csharp
public float Normalize()
```

Turns the current vector into a unit vector. The result is a vector one unit in length pointing in the same direction as the original vector.

### Returns

[float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6)

### Summary

```csharp
public static Vector3 Normalize(Vector3 value)
```

Creates a unit vector from the specified vector. The result is a vector one unit in length pointing in the same direction as the original vector.

### Returns

[Vector3](VRageMath.Vector3)

### Parameters

* [Vector3](VRageMath.Vector3) value
### Summary

```csharp
public static Vector3 Normalize(Vector3D value)
```

### Returns

[Vector3](VRageMath.Vector3)

### Parameters

* [Vector3D](VRageMath.Vector3D) value
### Summary

```csharp
public static void Normalize(ref Vector3 value, out Vector3 result)
```

Creates a unit vector from the specified vector, writing the result to a user-specified variable. The result is a vector one unit in length pointing in the same direction as the original vector.

### Parameters

* [Vector3](VRageMath.Vector3) value
* [Vector3](VRageMath.Vector3) result
