← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3L](VRageMath.Vector3L)

### Summary

```csharp
public static Vector3L Sign(Vector3 value)
```

### Returns

[Vector3L](VRageMath.Vector3L)

### Parameters

* [Vector3](VRageMath.Vector3) value
### Summary

```csharp
public static Vector3L Sign(Vector3L value)
```

### Returns

[Vector3L](VRageMath.Vector3L)

### Parameters

* [Vector3L](VRageMath.Vector3L) value
