← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector4D](VRageMath.Vector4D)

### Summary

```csharp
public static Vector4 Subtract(Vector4 value1, Vector4 value2)
```

Subtracts a vector from a vector.

### Returns

[Vector4](VRageMath.Vector4)

### Parameters

* [Vector4](VRageMath.Vector4) value1
* [Vector4](VRageMath.Vector4) value2
### Summary

```csharp
public static void Subtract(ref Vector4D value1, ref Vector4D value2, out Vector4D result)
```

Subtracts a vector from a vector.

### Parameters

* [Vector4D](VRageMath.Vector4D) value1
* [Vector4D](VRageMath.Vector4D) value2
* [Vector4D](VRageMath.Vector4D) result
