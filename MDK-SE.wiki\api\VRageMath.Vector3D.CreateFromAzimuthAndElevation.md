← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public static void CreateFromAzimuthAndElevation(double azimuth, double elevation, out Vector3D direction)
```

### Parameters

* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) azimuth
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) elevation
* [Vector3D](VRageMath.Vector3D) direction
