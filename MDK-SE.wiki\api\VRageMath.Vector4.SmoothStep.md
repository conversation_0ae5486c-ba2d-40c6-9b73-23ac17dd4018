← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector4](VRageMath.Vector4)

### Summary

```csharp
public static Vector4 SmoothStep(Vector4 value1, Vector4 value2, float amount)
```

Interpolates between two values using a cubic equation.

### Returns

[Vector4](VRageMath.Vector4)

### Parameters

* [Vector4](VRageMath.Vector4) value1
* [Vector4](VRageMath.Vector4) value2
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) amount
### Summary

```csharp
public static void SmoothStep(ref Vector4 value1, ref Vector4 value2, float amount, out Vector4 result)
```

Interpolates between two values using a cubic equation.

### Parameters

* [Vector4](VRageMath.Vector4) value1
* [Vector4](VRageMath.Vector4) value2
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) amount
* [Vector4](VRageMath.Vector4) result
