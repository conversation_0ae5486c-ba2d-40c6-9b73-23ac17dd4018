← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector4I](VRageMath.Vector4I)

### Summary

```csharp
public Vector4I(int x, int y, int z, int w)
```

### Parameters

* [int](https://docs.microsoft.com/en-us/dotnet/api/System.Int32?view=netframework-4.6) x
* [int](https://docs.microsoft.com/en-us/dotnet/api/System.Int32?view=netframework-4.6) y
* [int](https://docs.microsoft.com/en-us/dotnet/api/System.Int32?view=netframework-4.6) z
* [int](https://docs.microsoft.com/en-us/dotnet/api/System.Int32?view=netframework-4.6) w
### Summary

```csharp
public Vector4I(Vector3I xyz, int w)
```

### Parameters

* [Vector3I](VRageMath.Vector3I) xyz
* [int](https://docs.microsoft.com/en-us/dotnet/api/System.Int32?view=netframework-4.6) w
