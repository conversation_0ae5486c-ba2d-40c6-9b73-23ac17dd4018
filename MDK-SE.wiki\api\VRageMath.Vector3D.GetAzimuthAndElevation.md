← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public static void GetAzimuthAndElevation(Vector3D v, out double azimuth, out double elevation)
```

### Parameters

* [Vector3D](VRageMath.Vector3D) v
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) azimuth
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) elevation
