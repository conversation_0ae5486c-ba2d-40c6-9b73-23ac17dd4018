← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3](VRageMath.Vector3)

### Summary

```csharp
public static Vector3 ProjectOnPlane(ref Vector3 vec, ref Vector3 planeNormal)
```

Projects given vector on plane specified by it's normal.

### Returns

[Vector3](VRageMath.Vector3)

Vector projected on plane

### Parameters

* [Vector3](VRageMath.Vector3) vec
* [Vector3](VRageMath.Vector3) planeNormal
