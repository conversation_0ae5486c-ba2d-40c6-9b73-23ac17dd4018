← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public static double DistanceSquared(Vector3D value1, Vector3D value2)
```

Calculates the distance between two vectors squared.

### Returns

[double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6)

### Parameters

* [Vector3D](VRageMath.Vector3D) value1
* [Vector3D](VRageMath.Vector3D) value2
### Summary

```csharp
public static void DistanceSquared(ref Vector3D value1, ref Vector3D value2, out double result)
```

Calculates the distance between two vectors squared.

### Parameters

* [Vector3D](VRageMath.Vector3D) value1
* [Vector3D](VRageMath.Vector3D) value2
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) result
