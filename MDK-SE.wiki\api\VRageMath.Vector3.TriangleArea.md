← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3](VRageMath.Vector3)

### Summary

```csharp
public static float TriangleArea(Vector3 v1, Vector3 v2, Vector3 v3)
```

### Returns

[float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6)

### Parameters

* [Vector3](VRageMath.Vector3) v1
* [Vector3](VRageMath.Vector3) v2
* [Vector3](VRageMath.Vector3) v3
### Summary

```csharp
public static float TriangleArea(ref Vector3 v1, ref Vector3 v2, ref Vector3 v3)
```

### Returns

[float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6)

### Parameters

* [Vector3](VRageMath.Vector3) v1
* [Vector3](VRageMath.Vector3) v2
* [Vector3](VRageMath.Vector3) v3
