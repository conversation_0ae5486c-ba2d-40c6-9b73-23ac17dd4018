← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3](VRageMath.Vector3)

### Summary

```csharp
public Vector3(float x, float y, float z)
```

### Parameters

* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) x
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) y
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) z
### Summary

```csharp
public Vector3(double x, double y, double z)
```

### Parameters

* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) x
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) y
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) z
### Summary

```csharp
public Vector3(float value)
```

### Parameters

* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) value
### Summary

```csharp
public Vector3(Vector2 value, float z)
```

### Parameters

* [Vector2](VRageMath.Vector2) value
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) z
### Summary

```csharp
public Vector3(Vector4 xyz)
```

### Parameters

* [Vector4](VRageMath.Vector4) xyz
### Summary

```csharp
public Vector3(ref Vector3I value)
```

### Parameters

* [Vector3I](VRageMath.Vector3I) value
### Summary

```csharp
public Vector3(Vector3I value)
```

### Parameters

* [Vector3I](VRageMath.Vector3I) value
