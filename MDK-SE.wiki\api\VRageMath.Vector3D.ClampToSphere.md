← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public static Vector3D ClampToSphere(Vector3D vector, double radius, bool force = default)
```

### Returns

[Vector3D](VRageMath.Vector3D)

### Parameters

* [Vector3D](VRageMath.Vector3D) vector
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) radius
* [bool](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean?view=netframework-4.6) force
### Summary

```csharp
public static void ClampToSphere(ref Vector3D vector, double radius)
```

### Parameters

* [Vector3D](VRageMath.Vector3D) vector
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) radius
