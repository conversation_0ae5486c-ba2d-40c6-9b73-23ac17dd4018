← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public bool Equals(Vector3D other)
```

Determines whether the specified Object is equal to the Vector3.

### Returns

[bool](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean?view=netframework-4.6)

### Parameters

* [Vector3D](VRageMath.Vector3D) other
### Summary

```csharp
public bool Equals(Vector3D other, double epsilon)
```

### Returns

[bool](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean?view=netframework-4.6)

### Parameters

* [Vector3D](VRageMath.Vector3D) other
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) epsilon
### Summary

```csharp
public virtual bool Equals(object obj)
```

Returns a value that indicates whether the current instance is equal to a specified object.

### Returns

[bool](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean?view=netframework-4.6)

### Parameters

* [object](https://docs.microsoft.com/en-us/dotnet/api/System.Object?view=netframework-4.6) obj
