← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3](VRageMath.Vector3)

### Summary

```csharp
public static Vector3 Divide(Vector3 value1, Vector3 value2)
```

Divides the components of a vector by the components of another vector.

### Returns

[Vector3](VRageMath.Vector3)

### Parameters

* [Vector3](VRageMath.Vector3) value1
* [Vector3](VRageMath.Vector3) value2
### Summary

```csharp
public static void Divide(ref Vector3 value1, ref Vector3 value2, out Vector3 result)
```

Divides the components of a vector by the components of another vector.

### Parameters

* [Vector3](VRageMath.Vector3) value1
* [Vector3](VRageMath.Vector3) value2
* [Vector3](VRageMath.Vector3) result
### Summary

```csharp
public static Vector3 Divide(Vector3 value1, float value2)
```

Divides a vector by a scalar value.

### Returns

[Vector3](VRageMath.Vector3)

### Parameters

* [Vector3](VRageMath.Vector3) value1
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) value2
### Summary

```csharp
public static void Divide(ref Vector3 value1, float value2, out Vector3 result)
```

Divides a vector by a scalar value.

### Parameters

* [Vector3](VRageMath.Vector3) value1
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) value2
* [Vector3](VRageMath.Vector3) result
### Summary

```csharp
public void Divide(float divider)
```

### Parameters

* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) divider
