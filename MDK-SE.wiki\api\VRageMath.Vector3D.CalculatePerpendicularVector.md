← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public static Vector3D CalculatePerpendicularVector(Vector3D v)
```

### Returns

[Vector3D](VRageMath.Vector3D)

### Parameters

* [Vector3D](VRageMath.Vector3D) v
### Summary

```csharp
public void CalculatePerpendicularVector(out Vector3D result)
```

### Parameters

* [Vector3D](VRageMath.Vector3D) result
