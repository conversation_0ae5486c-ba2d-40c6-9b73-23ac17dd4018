← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector4](VRageMath.Vector4)

### Summary

```csharp
public bool Equals(Vector4 other)
```

Determines whether the specified Object is equal to the Vector4.

### Returns

[bool](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean?view=netframework-4.6)

### Parameters

* [Vector4](VRageMath.Vector4) other
### Summary

```csharp
public virtual bool Equals(object obj)
```

Returns a value that indicates whether the current instance is equal to a specified object.

### Returns

[bool](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean?view=netframework-4.6)

### Parameters

* [object](https://docs.microsoft.com/en-us/dotnet/api/System.Object?view=netframework-4.6) obj
