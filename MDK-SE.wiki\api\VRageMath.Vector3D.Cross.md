← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public Vector3D Cross(Vector3D v)
```

### Returns

[Vector3D](VRageMath.Vector3D)

### Parameters

* [Vector3D](VRageMath.Vector3D) v
### Summary

```csharp
public static Vector3D Cross(Vector3D vector1, Vector3D vector2)
```

Calculates the cross product of two vectors.

### Returns

[Vector3D](VRageMath.Vector3D)

### Parameters

* [Vector3D](VRageMath.Vector3D) vector1
* [Vector3D](VRageMath.Vector3D) vector2
### Summary

```csharp
public static void Cross(ref Vector3D vector1, ref Vector3D vector2, out Vector3D result)
```

Calculates the cross product of two vectors.

### Parameters

* [Vector3D](VRageMath.Vector3D) vector1
* [Vector3D](VRageMath.Vector3D) vector2
* [Vector3D](VRageMath.Vector3D) result
