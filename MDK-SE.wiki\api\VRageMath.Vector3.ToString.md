← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3](VRageMath.Vector3)

### Summary

```csharp
public virtual string ToString()
```

Retrieves a string representation of the current object.

### Returns

[string](https://docs.microsoft.com/en-us/dotnet/api/System.String?view=netframework-4.6)

### Summary

```csharp
public string ToString(string format)
```

### Returns

[string](https://docs.microsoft.com/en-us/dotnet/api/System.String?view=netframework-4.6)

### Parameters

* [string](https://docs.microsoft.com/en-us/dotnet/api/System.String?view=netframework-4.6) format
