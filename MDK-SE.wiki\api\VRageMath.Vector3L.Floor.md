← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3L](VRageMath.Vector3L)

### Summary

```csharp
public static Vector3L Floor(Vector3 value)
```

### Returns

[Vector3L](VRageMath.Vector3L)

### Parameters

* [Vector3](VRageMath.Vector3) value
### Summary

```csharp
public static Vector3L Floor(Vector3D value)
```

### Returns

[Vector3L](VRageMath.Vector3L)

### Parameters

* [Vector3D](VRageMath.Vector3D) value
### Summary

```csharp
public static void Floor(ref Vector3 v, out Vector3L r)
```

### Parameters

* [Vector3](VRageMath.Vector3) v
* [Vector3L](VRageMath.Vector3L) r
### Summary

```csharp
public static void Floor(ref Vector3D v, out Vector3L r)
```

### Parameters

* [Vector3D](VRageMath.Vector3D) v
* [Vector3L](VRageMath.Vector3L) r
