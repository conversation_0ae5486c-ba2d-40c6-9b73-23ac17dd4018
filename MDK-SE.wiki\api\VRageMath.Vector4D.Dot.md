← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector4D](VRageMath.Vector4D)

### Summary

```csharp
public static double Dot(Vector4 vector1, Vector4 vector2)
```

Calculates the dot product of two vectors.

### Returns

[double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6)

### Parameters

* [Vector4](VRageMath.Vector4) vector1
* [Vector4](VRageMath.Vector4) vector2
### Summary

```csharp
public static void Dot(ref Vector4 vector1, ref Vector4 vector2, out double result)
```

Calculates the dot product of two vectors.

### Parameters

* [Vector4](VRageMath.Vector4) vector1
* [Vector4](VRageMath.Vector4) vector2
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) result
