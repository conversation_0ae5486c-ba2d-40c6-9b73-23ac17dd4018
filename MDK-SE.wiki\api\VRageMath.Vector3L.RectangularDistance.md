← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3L](VRageMath.Vector3L)

### Summary

```csharp
public long RectangularDistance(Vector3L otherVector)
```

Calculates rectangular distance. It's how many sectors you have to travel to get to other sector from current sector.

### Returns

[long](https://docs.microsoft.com/en-us/dotnet/api/System.Int64?view=netframework-4.6)

### Parameters

* [Vector3L](VRageMath.Vector3L) otherVector
