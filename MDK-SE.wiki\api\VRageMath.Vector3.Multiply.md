← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3](VRageMath.Vector3)

### Summary

```csharp
public static Vector3 Multiply(Vector3 value1, Vector3 value2)
```

Multiplies the components of two vectors by each other.

### Returns

[Vector3](VRageMath.Vector3)

### Parameters

* [Vector3](VRageMath.Vector3) value1
* [Vector3](VRageMath.Vector3) value2
### Summary

```csharp
public static void Multiply(ref Vector3 value1, ref Vector3 value2, out Vector3 result)
```

Multiplies the components of two vectors by each other.

### Parameters

* [Vector3](VRageMath.Vector3) value1
* [Vector3](VRageMath.Vector3) value2
* [Vector3](VRageMath.Vector3) result
### Summary

```csharp
public static Vector3 Multiply(Vector3 value1, float scaleFactor)
```

Multiplies a vector by a scalar value.

### Returns

[Vector3](VRageMath.Vector3)

### Parameters

* [Vector3](VRageMath.Vector3) value1
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) scaleFactor
### Summary

```csharp
public static void Multiply(ref Vector3 value1, float scaleFactor, out Vector3 result)
```

Multiplies a vector by a scalar value.

### Parameters

* [Vector3](VRageMath.Vector3) value1
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) scaleFactor
* [Vector3](VRageMath.Vector3) result
### Summary

```csharp
public void Multiply(float scale)
```

### Parameters

* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) scale
