← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3](VRageMath.Vector3)

### Summary

```csharp
public static Vector3 ProjectOnVector(ref Vector3 vec, ref Vector3 guideVector)
```

Projects vector on another vector resulting in new vector in guided vector's direction with different length.

### Returns

[Vector3](VRageMath.Vector3)

Vector projected on guide vector

### Parameters

* [Vector3](VRageMath.Vector3) vec
* [Vector3](VRageMath.Vector3) guideVector
