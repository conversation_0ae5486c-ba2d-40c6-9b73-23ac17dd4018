← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public Vector3D(double x, double y, double z)
```

### Parameters

* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) x
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) y
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) z
### Summary

```csharp
public Vector3D(double value)
```

### Parameters

* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) value
### Summary

```csharp
public Vector3D(Vector2 value, double z)
```

### Parameters

* [Vector2](VRageMath.Vector2) value
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) z
### Summary

```csharp
public Vector3D(Vector2D value, double z)
```

### Parameters

* [Vector2D](VRageMath.Vector2D) value
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) z
### Summary

```csharp
public Vector3D(Vector4 xyz)
```

### Parameters

* [Vector4](VRageMath.Vector4) xyz
### Summary

```csharp
public Vector3D(Vector4D xyz)
```

### Parameters

* [Vector4D](VRageMath.Vector4D) xyz
### Summary

```csharp
public Vector3D(Vector3 value)
```

### Parameters

* [Vector3](VRageMath.Vector3) value
### Summary

```csharp
public Vector3D(ref Vector3I value)
```

### Parameters

* [Vector3I](VRageMath.Vector3I) value
### Summary

```csharp
public Vector3D(Vector3I value)
```

### Parameters

* [Vector3I](VRageMath.Vector3I) value
### Summary

```csharp
public Vector3D(Vector3D value)
```

### Parameters

* [Vector3D](VRageMath.Vector3D) value
