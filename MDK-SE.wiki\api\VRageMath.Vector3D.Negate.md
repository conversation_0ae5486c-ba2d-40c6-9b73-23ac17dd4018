← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public static Vector3D Negate(Vector3D value)
```

Returns a vector pointing in the opposite direction.

### Returns

[Vector3D](VRageMath.Vector3D)

### Parameters

* [Vector3D](VRageMath.Vector3D) value
### Summary

```csharp
public static void Negate(ref Vector3D value, out Vector3D result)
```

Returns a vector pointing in the opposite direction.

### Parameters

* [Vector3D](VRageMath.Vector3D) value
* [Vector3D](VRageMath.Vector3D) result
