← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public static Vector3D Multiply(Vector3D value1, Vector3D value2)
```

Multiplies the components of two vectors by each other.

### Returns

[Vector3D](VRageMath.Vector3D)

### Parameters

* [Vector3D](VRageMath.Vector3D) value1
* [Vector3D](VRageMath.Vector3D) value2
### Summary

```csharp
public static void Multiply(ref Vector3D value1, ref Vector3D value2, out Vector3D result)
```

Multiplies the components of two vectors by each other.

### Parameters

* [Vector3D](VRageMath.Vector3D) value1
* [Vector3D](VRageMath.Vector3D) value2
* [Vector3D](VRageMath.Vector3D) result
### Summary

```csharp
public static Vector3D Multiply(Vector3D value1, double scaleFactor)
```

Multiplies a vector by a scalar value.

### Returns

[Vector3D](VRageMath.Vector3D)

### Parameters

* [Vector3D](VRageMath.Vector3D) value1
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) scaleFactor
### Summary

```csharp
public static void Multiply(ref Vector3D value1, double scaleFactor, out Vector3D result)
```

Multiplies a vector by a scalar value.

### Parameters

* [Vector3D](VRageMath.Vector3D) value1
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) scaleFactor
* [Vector3D](VRageMath.Vector3D) result
