← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3](VRageMath.Vector3)

### Summary

```csharp
public static float RectangularDistance(Vector3 value1, Vector3 value2)
```

Calculates rectangular distance (a.k.a. Manhattan distance or Chessboard distace) between two vectors.

### Returns

[float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6)

### Parameters

* [Vector3](VRageMath.Vector3) value1
* [Vector3](VRageMath.Vector3) value2
### Summary

```csharp
public static float RectangularDistance(ref Vector3 value1, ref Vector3 value2)
```

Calculates rectangular distance (a.k.a. Manhattan distance or Chessboard distace) between two vectors.

### Returns

[float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6)

### Parameters

* [Vector3](VRageMath.Vector3) value1
* [Vector3](VRageMath.Vector3) value2
