#!/usr/bin/env python3
"""
Extract C# code without comments - simple and reliable approach
"""

def clean_code(content):
    lines = content.split('\n')
    cleaned_lines = []
    
    for line in lines:
        # Remove inline comments (simple approach)
        if '//' in line:
            # Find // that's not in a string
            in_string = False
            i = 0
            while i < len(line) - 1:
                if line[i] == '"' and (i == 0 or line[i-1] != '\\'):
                    in_string = not in_string
                elif not in_string and line[i:i+2] == '//':
                    line = line[:i].rstrip()
                    break
                i += 1
        
        # Skip lines that are pure comments
        stripped = line.strip()
        if stripped.startswith('//') or stripped.startswith('/*') or stripped.startswith('*'):
            continue
            
        # Keep the line
        cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines)

def main():
    try:
        with open('FleetCommandScript.cs', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Remove block comments first
        while '/*' in content:
            start = content.find('/*')
            end = content.find('*/', start)
            if end == -1:
                break
            content = content[:start] + content[end+2:]
        
        # Clean line comments
        cleaned = clean_code(content)
        
        with open('FleetCommandScript_Clean.cs', 'w', encoding='utf-8') as f:
            f.write(cleaned)
        
        print("✅ Clean code extracted to FleetCommandScript_Clean.cs")
        print(f"📊 Original: {len(content.split())} lines")
        print(f"📊 Cleaned:  {len(cleaned.split())} lines")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
