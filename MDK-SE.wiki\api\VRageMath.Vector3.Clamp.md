← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3](VRageMath.Vector3)

### Summary

```csharp
public static Vector3 Clamp(Vector3 value1, Vector3 min, Vector3 max)
```

Restricts a value to be within a specified range.

### Returns

[Vector3](VRageMath.Vector3)

### Parameters

* [Vector3](VRageMath.Vector3) value1
* [Vector3](VRageMath.Vector3) min
* [Vector3](VRageMath.Vector3) max
### Summary

```csharp
public static Vector3 Clamp(Vector3 value1, Vector3 min, Vector3 max, out Vector3 clampedBy, out Vector3 minRemaining, out Vector3 maxRemaining)
```

Restricts a value to be within a specified range.

### Returns

[Vector3](VRageMath.Vector3)



### Parameters

* [Vector3](VRageMath.Vector3) value1
* [Vector3](VRageMath.Vector3) min
* [Vector3](VRageMath.Vector3) max
* [Vector3](VRageMath.Vector3) clampedBy
* [Vector3](VRageMath.Vector3) minRemaining
* [Vector3](VRageMath.Vector3) maxRemaining
### Summary

```csharp
public static void Clamp(ref Vector3 value1, ref Vector3 min, ref Vector3 max, out Vector3 result)
```

Restricts a value to be within a specified range.

### Parameters

* [Vector3](VRageMath.Vector3) value1
* [Vector3](VRageMath.Vector3) min
* [Vector3](VRageMath.Vector3) max
* [Vector3](VRageMath.Vector3) result
