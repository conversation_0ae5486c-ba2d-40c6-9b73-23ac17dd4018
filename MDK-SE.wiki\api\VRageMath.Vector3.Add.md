← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3](VRageMath.Vector3)

### Summary

```csharp
public static Vector3 Add(Vector3 value1, Vector3 value2)
```

Adds two vectors.

### Returns

[Vector3](VRageMath.Vector3)

### Parameters

* [Vector3](VRageMath.Vector3) value1
* [Vector3](VRageMath.Vector3) value2
### Summary

```csharp
public static void Add(ref Vector3 value1, ref Vector3 value2, out Vector3 result)
```

Adds two vectors.

### Parameters

* [Vector3](VRageMath.Vector3) value1
* [Vector3](VRageMath.Vector3) value2
* [Vector3](VRageMath.Vector3) result
### Summary

```csharp
public void Add(Vector3 other)
```

### Parameters

* [Vector3](VRageMath.Vector3) other
