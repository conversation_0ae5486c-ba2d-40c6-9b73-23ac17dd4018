← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector4D](VRageMath.Vector4D)

### Summary

```csharp
public static Vector4D PackOrthoMatrix(Vector3D position, Vector3 forward, Vector3 up)
```

### Returns

[Vector4D](VRageMath.Vector4D)

### Parameters

* [Vector3D](VRageMath.Vector3D) position
* [Vector3](VRageMath.Vector3) forward
* [Vector3](VRageMath.Vector3) up
### Summary

```csharp
public static Vector4D PackOrthoMatrix(ref MatrixD matrix)
```

### Returns

[Vector4D](VRageMath.Vector4D)

### Parameters

* [MatrixD](VRageMath.MatrixD) matrix
