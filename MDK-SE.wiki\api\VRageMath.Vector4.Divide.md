← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector4](VRageMath.Vector4)

### Summary

```csharp
public static Vector4 Divide(Vector4 value1, Vector4 value2)
```

Divides the components of a vector by the components of another vector.

### Returns

[Vector4](VRageMath.Vector4)

### Parameters

* [Vector4](VRageMath.Vector4) value1
* [Vector4](VRageMath.Vector4) value2
### Summary

```csharp
public static void Divide(ref Vector4 value1, ref Vector4 value2, out Vector4 result)
```

Divides the components of a vector by the components of another vector.

### Parameters

* [Vector4](VRageMath.Vector4) value1
* [Vector4](VRageMath.Vector4) value2
* [Vector4](VRageMath.Vector4) result
### Summary

```csharp
public static Vector4 Divide(Vector4 value1, float divider)
```

Divides a vector by a scalar value.

### Returns

[Vector4](VRageMath.Vector4)

### Parameters

* [Vector4](VRageMath.Vector4) value1
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) divider
### Summary

```csharp
public static void Divide(ref Vector4 value1, float divider, out Vector4 result)
```

Divides a vector by a scalar value.

### Parameters

* [Vector4](VRageMath.Vector4) value1
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) divider
* [Vector4](VRageMath.Vector4) result
