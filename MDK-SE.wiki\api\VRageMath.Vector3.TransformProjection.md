← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3](VRageMath.Vector3)

### Summary

```csharp
public static void TransformProjection(ref Vector3 position, ref Matrix matrix, out Vector3 result)
```

Transforms a Vector3 by the given projection matrix (both ortho and perspective are supported)

### Parameters

* [Vector3](VRageMath.Vector3) position
* [Matrix](VRageMath.Matrix) matrix
* [Vector3](VRageMath.Vector3) result
