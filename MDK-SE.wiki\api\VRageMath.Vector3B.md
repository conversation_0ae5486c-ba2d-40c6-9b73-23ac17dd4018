← [Index](Api-Index) ← [Namespace Index](Namespace-Index)

#### Vector3B Struct

```csharp
public struct Vector3B
```

**Namespace:** [VRageMath](VRageMath)  
**Assembly:** VRage.Math.dll

#### Fields

[static Vector3B Backward](VRageMath.Vector3B.Backward)

> 

[static Vector3B Down](VRageMath.Vector3B.Down)

> 

[static Vector3B Forward](VRageMath.Vector3B.Forward)

> 

[static Vector3B Left](VRageMath.Vector3B.Left)

> 

[static Vector3B Right](VRageMath.Vector3B.Right)

> 

[static Vector3B Up](VRageMath.Vector3B.Up)

> 

[static Vector3B Zero](VRageMath.Vector3B.Zero)

> 

[sbyte X](VRageMath.Vector3B.X)

> 

[sbyte Y](VRageMath.Vector3B.Y)

> 

[sbyte Z](VRageMath.Vector3B.Z)

> 

#### Constructors

[Vector3B(sbyte x, sbyte y, sbyte z)](VRageMath.Vector3B..ctor)

> 

[Vector3B(Vector3I vec)](VRageMath.Vector3B..ctor)

> 

#### Methods

[static Vector3B Fit(Vector3 vec, float range)](VRageMath.Vector3B.Fit)

> Puts Vector3 into Vector3B, value -127 represents -range, 128 represents range

[static Vector3B Round(Vector3 vec)](VRageMath.Vector3B.Round)

> 

[bool Equals(object obj)](VRageMath.Vector3B.Equals)

> 

[int GetHashCode()](VRageMath.Vector3B.GetHashCode)

> 

[string ToString()](VRageMath.Vector3B.ToString)

> 

