#!/usr/bin/env python3
"""
Quick script to extract C# code without comments from FleetCommandScript.cs
Removes single-line comments (//) and multi-line comments (/* */)
Preserves strings that might contain comment-like text
"""

def remove_comments(code):
    """Remove C# comments while preserving strings and code structure"""

    lines = code.split('\n')
    result_lines = []
    in_multiline_comment = False

    for line in lines:
        original_line = line
        cleaned_line = ""
        i = 0
        in_string = False
        escape_next = False

        while i < len(line):
            char = line[i]

            # Handle string literals
            if char == '"' and not escape_next and not in_multiline_comment:
                in_string = not in_string
                cleaned_line += char
            elif in_string:
                cleaned_line += char
                if char == '\\' and not escape_next:
                    escape_next = True
                else:
                    escape_next = False
            # Handle multi-line comments
            elif not in_string and i < len(line) - 1 and line[i:i+2] == '/*':
                in_multiline_comment = True
                i += 1  # Skip the next character
            elif not in_string and in_multiline_comment and i < len(line) - 1 and line[i:i+2] == '*/':
                in_multiline_comment = False
                i += 1  # Skip the next character
            elif in_multiline_comment:
                pass  # Skip characters in multi-line comment
            # Handle single-line comments
            elif not in_string and not in_multiline_comment and i < len(line) - 1 and line[i:i+2] == '//':
                break  # Rest of line is comment
            else:
                if not in_multiline_comment:
                    cleaned_line += char

            i += 1

        # Keep the line if it has content or if it's needed for structure
        cleaned_line = cleaned_line.rstrip()
        if cleaned_line.strip() or (not cleaned_line.strip() and original_line.strip().startswith('#')):
            result_lines.append(cleaned_line)
        elif cleaned_line == "" and result_lines and result_lines[-1].strip():
            # Keep one empty line after code blocks
            result_lines.append("")

    # Remove excessive empty lines (more than 2 consecutive)
    final_lines = []
    empty_count = 0

    for line in result_lines:
        if not line.strip():
            empty_count += 1
            if empty_count <= 2:
                final_lines.append(line)
        else:
            empty_count = 0
            final_lines.append(line)

    return '\n'.join(final_lines)

def main():
    input_file = 'FleetCommandScript.cs'
    output_file = 'FleetCommandScript_NoComments.cs'
    
    try:
        # Read the input file
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Remove comments
        cleaned_content = remove_comments(content)
        
        # Write to output file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(cleaned_content)
        
        print("✅ Code extracted successfully!")
        print(f"📁 Input:  {input_file}")
        print(f"📁 Output: {output_file}")
        
        # Show some stats
        original_lines = len(content.split('\n'))
        cleaned_lines = len(cleaned_content.split('\n'))
        print(f"📊 Lines: {original_lines} → {cleaned_lines} ({original_lines - cleaned_lines} removed)")
        
    except FileNotFoundError:
        print(f"❌ Error: Could not find {input_file}")
        print("Make sure the file exists in the current directory.")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
