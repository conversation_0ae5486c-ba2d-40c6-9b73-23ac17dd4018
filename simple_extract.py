#!/usr/bin/env python3
"""
Simple script to remove comments from C# code
Removes lines that start with // and removes inline // comments
Removes /* */ block comments
"""

def remove_comments_simple(content):
    """Remove comments from C# code"""
    lines = content.split('\n')
    result_lines = []
    in_block_comment = False
    
    for line in lines:
        # Handle block comments
        while '/*' in line or '*/' in line or in_block_comment:
            if in_block_comment:
                if '*/' in line:
                    # End of block comment
                    end_pos = line.find('*/') + 2
                    line = line[end_pos:]
                    in_block_comment = False
                else:
                    # Entire line is in block comment
                    line = ''
                    break
            else:
                if '/*' in line:
                    # Start of block comment
                    start_pos = line.find('/*')
                    if '*/' in line[start_pos:]:
                        # Single line block comment
                        end_pos = line.find('*/', start_pos) + 2
                        line = line[:start_pos] + line[end_pos:]
                    else:
                        # Multi-line block comment starts
                        line = line[:start_pos]
                        in_block_comment = True
                        break
                else:
                    break
        
        # Handle single line comments (but not URLs or other //)
        if '//' in line and not in_block_comment:
            # Simple check: if // is not in a string, remove it
            comment_pos = line.find('//')
            # Basic string check - count quotes before //
            before_comment = line[:comment_pos]
            quote_count = before_comment.count('"') - before_comment.count('\\"')
            
            # If even number of quotes, we're not in a string
            if quote_count % 2 == 0:
                line = line[:comment_pos].rstrip()
        
        # Keep line if it has content or is needed for structure
        if line.strip() or (not result_lines or result_lines[-1].strip()):
            result_lines.append(line)
    
    # Clean up excessive empty lines
    final_lines = []
    prev_empty = False
    
    for line in result_lines:
        is_empty = not line.strip()
        if not (is_empty and prev_empty):
            final_lines.append(line)
        prev_empty = is_empty
    
    return '\n'.join(final_lines)

def main():
    input_file = 'FleetCommandScript.cs'
    output_file = 'FleetCommandScript_NoComments.cs'
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        cleaned_content = remove_comments_simple(content)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(cleaned_content)
        
        print("✅ Code extracted successfully!")
        print(f"📁 Input:  {input_file}")
        print(f"📁 Output: {output_file}")
        
        original_lines = len(content.split('\n'))
        cleaned_lines = len(cleaned_content.split('\n'))
        print(f"📊 Lines: {original_lines} → {cleaned_lines} ({original_lines - cleaned_lines} removed)")
        
    except FileNotFoundError:
        print(f"❌ Error: Could not find {input_file}")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
