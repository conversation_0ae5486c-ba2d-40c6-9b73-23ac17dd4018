← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3L](VRageMath.Vector3L)

### Summary

```csharp
public static Vector3L Max(Vector3L value1, Vector3L value2)
```

### Returns

[Vector3L](VRageMath.Vector3L)

### Parameters

* [Vector3L](VRageMath.Vector3L) value1
* [Vector3L](VRageMath.Vector3L) value2
### Summary

```csharp
public static void Max(ref Vector3L value1, ref Vector3L value2, out Vector3L result)
```

### Parameters

* [Vector3L](VRageMath.Vector3L) value1
* [Vector3L](VRageMath.Vector3L) value2
* [Vector3L](VRageMath.Vector3L) result
