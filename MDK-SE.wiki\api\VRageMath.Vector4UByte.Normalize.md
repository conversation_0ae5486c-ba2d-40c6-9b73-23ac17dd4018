← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector4UByte](VRageMath.Vector4UByte)

### Summary

```csharp
public static Vector4UByte Normalize(Vector3 vec, float range)
```

Normalizes Vector3 into Vector4UByte, scales vector from (-range, range) to (0, 255)

### Returns

[Vector4UByte](VRageMath.Vector4UByte)

### Parameters

* [Vector3](VRageMath.Vector3) vec
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) range
