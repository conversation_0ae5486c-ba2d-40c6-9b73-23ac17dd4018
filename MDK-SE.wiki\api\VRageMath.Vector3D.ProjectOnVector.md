← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public static Vector3D ProjectOnVector(ref Vector3D vec, ref Vector3D guideVector)
```

Projects vector on another vector resulting in new vector in guided vector's direction with different length.

### Returns

[Vector3D](VRageMath.Vector3D)

Vector projected on guide vector

### Parameters

* [Vector3D](VRageMath.Vector3D) vec
* [Vector3D](VRageMath.Vector3D) guideVector
