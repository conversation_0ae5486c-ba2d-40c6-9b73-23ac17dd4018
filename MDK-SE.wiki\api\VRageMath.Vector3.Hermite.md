← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3](VRageMath.Vector3)

### Summary

```csharp
public static Vector3 Hermite(Vector3 value1, Vector3 tangent1, Vector3 value2, Vector3 tangent2, float amount)
```

Performs a Hermite spline interpolation.

### Returns

[Vector3](VRageMath.Vector3)

### Parameters

* [Vector3](VRageMath.Vector3) value1
* [Vector3](VRageMath.Vector3) tangent1
* [Vector3](VRageMath.Vector3) value2
* [Vector3](VRageMath.Vector3) tangent2
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) amount
### Summary

```csharp
public static void Hermite(ref Vector3 value1, ref Vector3 tangent1, ref Vector3 value2, ref Vector3 tangent2, float amount, out Vector3 result)
```

Performs a Hermite spline interpolation.

### Parameters

* [Vector3](VRageMath.Vector3) value1
* [Vector3](VRageMath.Vector3) tangent1
* [Vector3](VRageMath.Vector3) value2
* [Vector3](VRageMath.Vector3) tangent2
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) amount
* [Vector3](VRageMath.Vector3) result
