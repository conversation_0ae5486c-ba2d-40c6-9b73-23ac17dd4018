← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public static double Dot(Vector3D vector1, Vector3D vector2)
```

Calculates the dot product of two vectors. If the two vectors are unit vectors, the dot product returns a doubleing point value between -1 and 1 that can be used to determine some properties of the angle between two vectors. For example, it can show whether the vectors are orthogonal, parallel, or have an acute or obtuse angle between them.

### Returns

[double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6)

### Parameters

* [Vector3D](VRageMath.Vector3D) vector1
* [Vector3D](VRageMath.Vector3D) vector2
### Summary

```csharp
public static double Dot(Vector3D vector1, Vector3 vector2)
```

### Returns

[double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6)

### Parameters

* [Vector3D](VRageMath.Vector3D) vector1
* [Vector3](VRageMath.Vector3) vector2
### Summary

```csharp
public static void Dot(ref Vector3D vector1, ref Vector3D vector2, out double result)
```

Calculates the dot product of two vectors and writes the result to a user-specified variable. If the two vectors are unit vectors, the dot product returns a doubleing point value between -1 and 1 that can be used to determine some properties of the angle between two vectors. For example, it can show whether the vectors are orthogonal, parallel, or have an acute or obtuse angle between them.

### Parameters

* [Vector3D](VRageMath.Vector3D) vector1
* [Vector3D](VRageMath.Vector3D) vector2
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) result
### Summary

```csharp
public static void Dot(ref Vector3D vector1, ref Vector3 vector2, out double result)
```

### Parameters

* [Vector3D](VRageMath.Vector3D) vector1
* [Vector3](VRageMath.Vector3) vector2
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) result
### Summary

```csharp
public static void Dot(ref Vector3 vector1, ref Vector3D vector2, out double result)
```

### Parameters

* [Vector3](VRageMath.Vector3) vector1
* [Vector3D](VRageMath.Vector3D) vector2
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) result
### Summary

```csharp
public double Dot(Vector3D v)
```

### Returns

[double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6)

### Parameters

* [Vector3D](VRageMath.Vector3D) v
### Summary

```csharp
public double Dot(Vector3 v)
```

### Returns

[double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6)

### Parameters

* [Vector3](VRageMath.Vector3) v
### Summary

```csharp
public double Dot(ref Vector3D v)
```

### Returns

[double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6)

### Parameters

* [Vector3D](VRageMath.Vector3D) v
