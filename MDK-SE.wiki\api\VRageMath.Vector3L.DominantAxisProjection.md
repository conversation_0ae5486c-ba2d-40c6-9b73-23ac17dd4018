← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3L](VRageMath.Vector3L)

### Summary

```csharp
public static Vector3L DominantAxisProjection(Vector3L value1)
```

Returns a vector that is equal to the projection of the input vector to the coordinate axis that corresponds to the original vector's largest value.

### Returns

[Vector3L](VRageMath.Vector3L)

### Parameters

* [Vector3L](VRageMath.Vector3L) value1
### Summary

```csharp
public static void DominantAxisProjection(ref Vector3L value1, out Vector3L result)
```

Calculates a vector that is equal to the projection of the input vector to the coordinate axis that corresponds to the original vector's largest value. The result is saved longo a user-specified variable.

### Parameters

* [Vector3L](VRageMath.Vector3L) value1
* [Vector3L](VRageMath.Vector3L) result
