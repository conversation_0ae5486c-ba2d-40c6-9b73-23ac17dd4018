← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3UByte](VRageMath.Vector3UByte)

### Summary

```csharp
public static Vector3 Denormalize(Vector3UByte vec, float range)
```

Unpacks Vector3 from Vector3UByte, scales vector from (0, 255) to (-range, range)

### Returns

[Vector3](VRageMath.Vector3)

### Parameters

* [Vector3UByte](VRageMath.Vector3UByte) vec
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) range
