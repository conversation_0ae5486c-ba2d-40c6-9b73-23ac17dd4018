← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public double Normalize()
```

Turns the current vector into a unit vector. The result is a vector one unit in length pointing in the same direction as the original vector.

### Returns

[double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6)

### Summary

```csharp
public static Vector3D Normalize(Vector3D value)
```

Creates a unit vector from the specified vector. The result is a vector one unit in length pointing in the same direction as the original vector.

### Returns

[Vector3D](VRageMath.Vector3D)

### Parameters

* [Vector3D](VRageMath.Vector3D) value
### Summary

```csharp
public static void Normalize(ref Vector3D value, out Vector3D result)
```

Creates a unit vector from the specified vector, writing the result to a user-specified variable. The result is a vector one unit in length pointing in the same direction as the original vector.

### Parameters

* [Vector3D](VRageMath.Vector3D) value
* [Vector3D](VRageMath.Vector3D) result
