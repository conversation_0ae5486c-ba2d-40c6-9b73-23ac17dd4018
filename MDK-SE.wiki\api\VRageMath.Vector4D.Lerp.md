← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector4D](VRageMath.Vector4D)

### Summary

```csharp
public static Vector4D Lerp(Vector4D value1, Vector4D value2, double amount)
```

Performs a linear interpolation between two vectors.

### Returns

[Vector4D](VRageMath.Vector4D)

### Parameters

* [Vector4D](VRageMath.Vector4D) value1
* [Vector4D](VRageMath.Vector4D) value2
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) amount
### Summary

```csharp
public static void Lerp(ref Vector4D value1, ref Vector4D value2, double amount, out Vector4D result)
```

Performs a linear interpolation between two vectors.

### Parameters

* [Vector4D](VRageMath.Vector4D) value1
* [Vector4D](VRageMath.Vector4D) value2
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) amount
* [Vector4D](VRageMath.Vector4D) result
