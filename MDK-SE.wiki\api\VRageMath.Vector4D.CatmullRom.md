← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector4D](VRageMath.Vector4D)

### Summary

```csharp
public static Vector4D CatmullRom(Vector4D value1, Vector4D value2, Vector4D value3, Vector4D value4, double amount)
```

Performs a Catmull-Rom interpolation using the specified positions.

### Returns

[Vector4D](VRageMath.Vector4D)

### Parameters

* [Vector4D](VRageMath.Vector4D) value1
* [Vector4D](VRageMath.Vector4D) value2
* [Vector4D](VRageMath.Vector4D) value3
* [Vector4D](VRageMath.Vector4D) value4
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) amount
### Summary

```csharp
public static void CatmullRom(ref Vector4D value1, ref Vector4D value2, ref Vector4D value3, ref Vector4D value4, double amount, out Vector4D result)
```

Performs a Catmull-Rom interpolation using the specified positions.

### Parameters

* [Vector4D](VRageMath.Vector4D) value1
* [Vector4D](VRageMath.Vector4D) value2
* [Vector4D](VRageMath.Vector4D) value3
* [Vector4D](VRageMath.Vector4D) value4
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) amount
* [Vector4D](VRageMath.Vector4D) result
