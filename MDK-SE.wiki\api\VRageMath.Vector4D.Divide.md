← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector4D](VRageMath.Vector4D)

### Summary

```csharp
public static Vector4D Divide(Vector4D value1, Vector4D value2)
```

Divides the components of a vector by the components of another vector.

### Returns

[Vector4D](VRageMath.Vector4D)

### Parameters

* [Vector4D](VRageMath.Vector4D) value1
* [Vector4D](VRageMath.Vector4D) value2
### Summary

```csharp
public static void Divide(ref Vector4D value1, ref Vector4D value2, out Vector4D result)
```

Divides the components of a vector by the components of another vector.

### Parameters

* [Vector4D](VRageMath.Vector4D) value1
* [Vector4D](VRageMath.Vector4D) value2
* [Vector4D](VRageMath.Vector4D) result
### Summary

```csharp
public static Vector4D Divide(Vector4D value1, double divider)
```

Divides a vector by a scalar value.

### Returns

[Vector4D](VRageMath.Vector4D)

### Parameters

* [Vector4D](VRageMath.Vector4D) value1
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) divider
### Summary

```csharp
public static void Divide(ref Vector4D value1, double divider, out Vector4D result)
```

Divides a vector by a scalar value.

### Parameters

* [Vector4D](VRageMath.Vector4D) value1
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) divider
* [Vector4D](VRageMath.Vector4D) result
