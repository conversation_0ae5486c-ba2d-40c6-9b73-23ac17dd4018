← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3S](VRageMath.Vector3S)

### Summary

```csharp
public Vector3S(Vector3I vec)
```

### Parameters

* [Vector3I](VRageMath.Vector3I) vec
### Summary

```csharp
public Vector3S(ref Vector3I vec)
```

### Parameters

* [Vector3I](VRageMath.Vector3I) vec
### Summary

```csharp
public Vector3S(short x, short y, short z)
```

### Parameters

* [short](https://docs.microsoft.com/en-us/dotnet/api/System.Int16?view=netframework-4.6) x
* [short](https://docs.microsoft.com/en-us/dotnet/api/System.Int16?view=netframework-4.6) y
* [short](https://docs.microsoft.com/en-us/dotnet/api/System.Int16?view=netframework-4.6) z
### Summary

```csharp
public Vector3S(float x, float y, float z)
```

### Parameters

* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) x
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) y
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) z
