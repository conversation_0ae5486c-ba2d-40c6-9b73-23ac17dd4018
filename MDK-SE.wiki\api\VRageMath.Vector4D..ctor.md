← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector4D](VRageMath.Vector4D)

### Summary

```csharp
public Vector4D(double x, double y, double z, double w)
```

### Parameters

* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) x
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) y
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) z
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) w
### Summary

```csharp
public Vector4D(Vector2 value, double z, double w)
```

### Parameters

* [Vector2](VRageMath.Vector2) value
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) z
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) w
### Summary

```csharp
public Vector4D(Vector3D value, double w)
```

### Parameters

* [Vector3D](VRageMath.Vector3D) value
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) w
### Summary

```csharp
public Vector4D(double value)
```

### Parameters

* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) value
