← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public static Vector3I Round(Vector3D vect3d)
```

### Returns

[Vector3I](VRageMath.Vector3I)

### Parameters

* [Vector3D](VRageMath.Vector3D) vect3d
### Summary

```csharp
public static Vector3D Round(Vector3D v, int numDecimals)
```

### Returns

[Vector3D](VRageMath.Vector3D)

### Parameters

* [Vector3D](VRageMath.Vector3D) v
* [int](https://docs.microsoft.com/en-us/dotnet/api/System.Int32?view=netframework-4.6) numDecimals
