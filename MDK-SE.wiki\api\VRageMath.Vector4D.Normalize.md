← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector4D](VRageMath.Vector4D)

### Summary

```csharp
public void Normalize()
```

Turns the current vector into a unit vector.

### Summary

```csharp
public static Vector4D Normalize(Vector4D vector)
```

Creates a unit vector from the specified vector.

### Returns

[Vector4D](VRageMath.Vector4D)

### Parameters

* [Vector4D](VRageMath.Vector4D) vector
### Summary

```csharp
public static void Normalize(ref Vector4D vector, out Vector4D result)
```

Returns a normalized version of the specified vector.

### Parameters

* [Vector4D](VRageMath.Vector4D) vector
* [Vector4D](VRageMath.Vector4D) result
