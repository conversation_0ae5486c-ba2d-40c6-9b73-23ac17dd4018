← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3](VRageMath.Vector3)

### Summary

```csharp
public static bool Is<PERSON>ero(ref Vector3 vec)
```

### Returns

[bool](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean?view=netframework-4.6)

### Parameters

* [Vector3](VRageMath.Vector3) vec
### Summary

```csharp
public static bool IsZero(Vector3 value)
```

### Returns

[bool](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean?view=netframework-4.6)

### Parameters

* [Vector3](VRageMath.Vector3) value
### Summary

```csharp
public static bool IsZero(Vector3 value, float epsilon)
```

### Returns

[bool](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean?view=netframework-4.6)

### Parameters

* [Vector3](VRageMath.Vector3) value
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) epsilon
