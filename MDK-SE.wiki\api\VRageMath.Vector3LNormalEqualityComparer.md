← [Index](Api-Index) ← [Namespace Index](Namespace-Index)

#### Vector3LNormalEqualityComparer Class

```csharp
public class Vector3LNormalEqualityComparer: IEqualityComparer<Vector3L>
```

This can be used only to compare normal vectors of Vector3L, where X, Y and Z has values -1, 0 or 1

**Namespace:** [VRageMath](VRageMath)  
**Assembly:** VRage.Math.dll

**Implements:**  
* [IEqualityComparer&lt;Vector3L&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEqualityComparer-1?view=netframework-4.6)

#### Constructors

[Vector3LNormalEqualityComparer()](VRageMath.Vector3LNormalEqualityComparer..ctor)

> 

#### Methods

[bool Equals(Vector3L x, Vector3L y)](VRageMath.Vector3LNormalEqualityComparer.Equals)

> 

[int GetHashCode(Vector3L x)](VRageMath.Vector3LNormalEqualityComparer.GetHashCode)

> 

