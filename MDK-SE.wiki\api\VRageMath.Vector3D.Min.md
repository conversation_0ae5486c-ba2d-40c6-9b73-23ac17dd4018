← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public double Min()
```

Returns the component of the vector that is smallest of all the three components.

### Returns

[double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6)

### Summary

```csharp
public static Vector3D Min(Vector3D value1, Vector3D value2)
```

Returns a vector that contains the lowest value from each matching pair of components.

### Returns

[Vector3D](VRageMath.Vector3D)

### Parameters

* [Vector3D](VRageMath.Vector3D) value1
* [Vector3D](VRageMath.Vector3D) value2
### Summary

```csharp
public static void Min(ref Vector3D value1, ref Vector3D value2, out Vector3D result)
```

Returns a vector that contains the lowest value from each matching pair of components.

### Parameters

* [Vector3D](VRageMath.Vector3D) value1
* [Vector3D](VRageMath.Vector3D) value2
* [Vector3D](VRageMath.Vector3D) result
