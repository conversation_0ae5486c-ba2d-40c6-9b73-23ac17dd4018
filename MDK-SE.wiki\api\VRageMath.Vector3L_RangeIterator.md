← [Index](Api-Index) ← [Namespace Index](Namespace-Index)

#### Vector3L_RangeIterator Struct

```csharp
public struct Vector3L_RangeIterator
```

A class for simpler traversal of ranges of long vectors

**Namespace:** [VRageMath](VRageMath)  
**Assembly:** VRage.Math.dll

#### Fields

[Vector3L Current](VRageMath.Vector3L_RangeIterator.Current)

> Do not modify, public only for optimization!

#### Constructors

[Vector3L_RangeIterator(ref Vector3L start, ref Vector3L end)](VRageMath.Vector3L_RangeIterator..ctor)

> 

#### Methods

[void GetNext(out Vector3L next)](VRageMath.Vector3L_RangeIterator.GetNext)

> 

[bool IsValid()](VRageMath.Vector3L_RangeIterator.IsValid)

> 

[void MoveNext()](VRageMath.Vector3L_RangeIterator.MoveNext)

> 

