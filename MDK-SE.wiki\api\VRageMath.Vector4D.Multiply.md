← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector4D](VRageMath.Vector4D)

### Summary

```csharp
public static Vector4D Multiply(Vector4D value1, Vector4D value2)
```

Multiplies the components of two vectors by each other.

### Returns

[Vector4D](VRageMath.Vector4D)

### Parameters

* [Vector4D](VRageMath.Vector4D) value1
* [Vector4D](VRageMath.Vector4D) value2
### Summary

```csharp
public static void Multiply(ref Vector4 value1, ref Vector4 value2, out Vector4 result)
```

Multiplies the components of two vectors by each other.

### Parameters

* [Vector4](VRageMath.Vector4) value1
* [Vector4](VRageMath.Vector4) value2
* [Vector4](VRageMath.Vector4) result
### Summary

```csharp
public static Vector4D Multiply(Vector4D value1, double scaleFactor)
```

Multiplies a vector by a scalar.

### Returns

[Vector4D](VRageMath.Vector4D)

### Parameters

* [Vector4D](VRageMath.Vector4D) value1
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) scaleFactor
### Summary

```csharp
public static void Multiply(ref Vector4D value1, double scaleFactor, out Vector4D result)
```

Multiplies a vector by a scalar value.

### Parameters

* [Vector4D](VRageMath.Vector4D) value1
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) scaleFactor
* [Vector4D](VRageMath.Vector4D) result
