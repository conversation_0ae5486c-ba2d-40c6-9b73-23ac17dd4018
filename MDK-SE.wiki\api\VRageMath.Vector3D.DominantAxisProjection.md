← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public static Vector3D DominantAxisProjection(Vector3D value1)
```

Returns a vector that is equal to the projection of the input vector to the coordinate axis that corresponds to the original vector's largest value.

### Returns

[Vector3D](VRageMath.Vector3D)

### Parameters

* [Vector3D](VRageMath.Vector3D) value1
### Summary

```csharp
public static void DominantAxisProjection(ref Vector3D value1, out Vector3D result)
```

Calculates a vector that is equal to the projection of the input vector to the coordinate axis that corresponds to the original vector's largest value. The result is saved into a user-specified variable.

### Parameters

* [Vector3D](VRageMath.Vector3D) value1
* [Vector3D](VRageMath.Vector3D) result
