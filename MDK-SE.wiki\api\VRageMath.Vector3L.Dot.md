← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3L](VRageMath.Vector3L)

### Summary

```csharp
public long Dot(ref Vector3L v)
```

### Returns

[long](https://docs.microsoft.com/en-us/dotnet/api/System.Int64?view=netframework-4.6)

### Parameters

* [Vector3L](VRageMath.Vector3L) v
### Summary

```csharp
public static long Dot(Vector3L vector1, Vector3L vector2)
```

### Returns

[long](https://docs.microsoft.com/en-us/dotnet/api/System.Int64?view=netframework-4.6)

### Parameters

* [Vector3L](VRageMath.Vector3L) vector1
* [Vector3L](VRageMath.Vector3L) vector2
### Summary

```csharp
public static long Dot(ref Vector3L vector1, ref Vector3L vector2)
```

### Returns

[long](https://docs.microsoft.com/en-us/dotnet/api/System.Int64?view=netframework-4.6)

### Parameters

* [Vector3L](VRageMath.Vector3L) vector1
* [Vector3L](VRageMath.Vector3L) vector2
### Summary

```csharp
public static void Dot(ref Vector3L vector1, ref Vector3L vector2, out long dot)
```

### Parameters

* [Vector3L](VRageMath.Vector3L) vector1
* [Vector3L](VRageMath.Vector3L) vector2
* [long](https://docs.microsoft.com/en-us/dotnet/api/System.Int64?view=netframework-4.6) dot
