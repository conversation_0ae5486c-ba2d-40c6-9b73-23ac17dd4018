← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public static Vector3D Reflect(Vector3D vector, Vector3D normal)
```

Returns the reflection of a vector off a surface that has the specified normal. Reference page contains code sample.

### Returns

[Vector3D](VRageMath.Vector3D)

### Parameters

* [Vector3D](VRageMath.Vector3D) vector
* [Vector3D](VRageMath.Vector3D) normal
### Summary

```csharp
public static void Reflect(ref Vector3D vector, ref Vector3D normal, out Vector3D result)
```

Returns the reflection of a vector off a surface that has the specified normal. Reference page contains code sample.

### Parameters

* [Vector3D](VRageMath.Vector3D) vector
* [Vector3D](VRageMath.Vector3D) normal
* [Vector3D](VRageMath.Vector3D) result
