← [Index](Api-Index) ← [Namespace Index](Namespace-Index)

#### Vector3S Struct

```csharp
public struct Vector3S
```

**Namespace:** [VRageMath](VRageMath)  
**Assembly:** VRage.Math.dll

#### Fields

[static Vector3S Backward](VRageMath.Vector3S.Backward)

> 

[static Vector3S Down](VRageMath.Vector3S.Down)

> 

[static Vector3S Forward](VRageMath.Vector3S.Forward)

> 

[static Vector3S Left](VRageMath.Vector3S.Left)

> 

[static Vector3S Right](VRageMath.Vector3S.Right)

> 

[static Vector3S Up](VRageMath.Vector3S.Up)

> 

[short X](VRageMath.Vector3S.X)

> 

[short Y](VRageMath.Vector3S.Y)

> 

[short Z](VRageMath.Vector3S.Z)

> 

#### Constructors

[Vector3S(Vector3I vec)](VRageMath.Vector3S..ctor)

> 

[Vector3S(ref Vector3I vec)](VRageMath.Vector3S..ctor)

> 

[Vector3S(short x, short y, short z)](VRageMath.Vector3S..ctor)

> 

[Vector3S(float x, float y, float z)](VRageMath.Vector3S..ctor)

> 

#### Methods

[static Vector3S Round(Vector3 v)](VRageMath.Vector3S.Round)

> 

[bool Equals(object obj)](VRageMath.Vector3S.Equals)

> 

[int GetHashCode()](VRageMath.Vector3S.GetHashCode)

> 

[string ToString()](VRageMath.Vector3S.ToString)

> 

