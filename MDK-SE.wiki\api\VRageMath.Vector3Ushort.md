← [Index](Api-Index) ← [Namespace Index](Namespace-Index)

#### Vector3Ushort Struct

```csharp
public struct Vector3Ushort
```

**Namespace:** [VRageMath](VRageMath)  
**Assembly:** VRage.Math.dll

#### Fields

[ushort X](VRageMath.Vector3Ushort.X)

> 

[ushort Y](VRageMath.Vector3Ushort.Y)

> 

[ushort Z](VRageMath.Vector3Ushort.Z)

> 

#### Constructors

[Vector3Ushort(ushort x, ushort y, ushort z)](VRageMath.Vector3Ushort..ctor)

> 

#### Methods

[string ToString()](VRageMath.Vector3Ushort.ToString)

> 

