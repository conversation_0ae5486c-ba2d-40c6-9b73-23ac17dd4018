← [Index](Api-Index) ← [Namespace Index](Namespace-Index)

#### Vector4I Struct

```csharp
public struct Vector4I: IComparable<Vector4I>
```

**Namespace:** [VRageMath](VRageMath)  
**Assembly:** VRage.Math.dll

**Implements:**  
* [IComparable&lt;Vector4I&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.IComparable-1?view=netframework-4.6)

#### Fields

[static EqualityComparer Comparer](VRageMath.Vector4I.Comparer)

> 

[int W](VRageMath.Vector4I.W)

> 

[int X](VRageMath.Vector4I.X)

> 

[int Y](VRageMath.Vector4I.Y)

> 

[int Z](VRageMath.Vector4I.Z)

> 

#### Properties

[int Item { get; set; }](VRageMath.Vector4I.Item)

> 

#### Constructors

[Vector4I(int x, int y, int z, int w)](VRageMath.Vector4I..ctor)

> 

[Vector4I(Vector3I xyz, int w)](VRageMath.Vector4I..ctor)

> 

#### Methods

[int CompareTo(Vector4I other)](VRageMath.Vector4I.CompareTo)

> 

[string ToString()](VRageMath.Vector4I.ToString)

> 

