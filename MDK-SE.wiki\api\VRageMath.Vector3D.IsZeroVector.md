← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public static Vector3D IsZeroVector(Vector3D value)
```

### Returns

[Vector3D](VRageMath.Vector3D)

### Parameters

* [Vector3D](VRageMath.Vector3D) value
### Summary

```csharp
public static Vector3D IsZeroVector(Vector3D value, double epsilon)
```

### Returns

[Vector3D](VRageMath.Vector3D)

### Parameters

* [Vector3D](VRageMath.Vector3D) value
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) epsilon
