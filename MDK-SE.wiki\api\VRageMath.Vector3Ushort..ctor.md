← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3Ushort](VRageMath.Vector3Ushort)

### Summary

```csharp
public Vector3Ushort(ushort x, ushort y, ushort z)
```

### Parameters

* [ushort](https://docs.microsoft.com/en-us/dotnet/api/System.UInt16?view=netframework-4.6) x
* [ushort](https://docs.microsoft.com/en-us/dotnet/api/System.UInt16?view=netframework-4.6) y
* [ushort](https://docs.microsoft.com/en-us/dotnet/api/System.UInt16?view=netframework-4.6) z
