← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public static void Abs(ref Vector3D vector3D, out Vector3D abs)
```

### Parameters

* [Vector3D](VRageMath.Vector3D) vector3D
* [Vector3D](VRageMath.Vector3D) abs
### Summary

```csharp
public static Vector3D Abs(Vector3D value)
```

### Returns

[Vector3D](VRageMath.Vector3D)

### Parameters

* [Vector3D](VRageMath.Vector3D) value
