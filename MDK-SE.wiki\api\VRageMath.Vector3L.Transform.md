← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3L](VRageMath.Vector3L)

### Summary

```csharp
public static void Transform(ref Vector3L position, ref Matrix matrix, out Vector3L result)
```

Transforms a Vector3L by the given Matrix.

### Parameters

* [Vector3L](VRageMath.Vector3L) position
* [Matrix](VRageMath.Matrix) matrix
* [Vector3L](VRageMath.Vector3L) result
### Summary

```csharp
public static void Transform(ref Vector3L value, ref Quaternion rotation, out Vector3L result)
```

### Parameters

* [Vector3L](VRageMath.Vector3L) value
* [Quaternion](VRageMath.Quaternion) rotation
* [Vector3L](VRageMath.Vector3L) result
### Summary

```csharp
public static Vector3L Transform(Vector3L value, Quaternion rotation)
```

### Returns

[Vector3L](VRageMath.Vector3L)

### Parameters

* [Vector3L](VRageMath.Vector3L) value
* [Quaternion](VRageMath.Quaternion) rotation
