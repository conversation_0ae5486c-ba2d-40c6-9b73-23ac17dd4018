← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public static double Distance(Vector3D value1, Vector3D value2)
```

Calculates the distance between two vectors.

### Returns

[double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6)

### Parameters

* [Vector3D](VRageMath.Vector3D) value1
* [Vector3D](VRageMath.Vector3D) value2
### Summary

```csharp
public static double Distance(Vector3D value1, Vector3 value2)
```

### Returns

[double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6)

### Parameters

* [Vector3D](VRageMath.Vector3D) value1
* [Vector3](VRageMath.Vector3) value2
### Summary

```csharp
public static double Distance(Vector3 value1, Vector3D value2)
```

### Returns

[double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6)

### Parameters

* [Vector3](VRageMath.Vector3) value1
* [Vector3D](VRageMath.Vector3D) value2
### Summary

```csharp
public static void Distance(ref Vector3D value1, ref Vector3D value2, out double result)
```

Calculates the distance between two vectors.

### Parameters

* [Vector3D](VRageMath.Vector3D) value1
* [Vector3D](VRageMath.Vector3D) value2
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) result
