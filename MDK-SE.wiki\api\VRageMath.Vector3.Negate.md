← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3](VRageMath.Vector3)

### Summary

```csharp
public static Vector3 Negate(Vector3 value)
```

Returns a vector pointing in the opposite direction.

### Returns

[Vector3](VRageMath.Vector3)

### Parameters

* [Vector3](VRageMath.Vector3) value
### Summary

```csharp
public static void Negate(ref Vector3 value, out Vector3 result)
```

Returns a vector pointing in the opposite direction.

### Parameters

* [Vector3](VRageMath.Vector3) value
* [Vector3](VRageMath.Vector3) result
