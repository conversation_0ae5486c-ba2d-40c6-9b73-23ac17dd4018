← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3](VRageMath.Vector3)

### Summary

```csharp
public Vector3 Cross(Vector3 v)
```

### Returns

[Vector3](VRageMath.Vector3)

### Parameters

* [Vector3](VRageMath.Vector3) v
### Summary

```csharp
public static Vector3 Cross(Vector3 vector1, Vector3 vector2)
```

Calculates the cross product of two vectors.

### Returns

[Vector3](VRageMath.Vector3)

### Parameters

* [Vector3](VRageMath.Vector3) vector1
* [Vector3](VRageMath.Vector3) vector2
### Summary

```csharp
public static void Cross(ref Vector3 vector1, ref Vector3 vector2, out Vector3 result)
```

Calculates the cross product of two vectors.

### Parameters

* [Vector3](VRageMath.Vector3) vector1
* [Vector3](VRageMath.Vector3) vector2
* [Vector3](VRageMath.Vector3) result
