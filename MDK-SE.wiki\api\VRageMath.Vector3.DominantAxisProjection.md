← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3](VRageMath.Vector3)

### Summary

```csharp
public static Vector3 DominantAxisProjection(Vector3 value1)
```

Returns a vector that is equal to the projection of the input vector to the coordinate axis that corresponds to the original vector's largest value.

### Returns

[Vector3](VRageMath.Vector3)

### Parameters

* [Vector3](VRageMath.Vector3) value1
### Summary

```csharp
public static void DominantAxisProjection(ref Vector3 value1, out Vector3 result)
```

Calculates a vector that is equal to the projection of the input vector to the coordinate axis that corresponds to the original vector's largest value. The result is saved into a user-specified variable.

### Parameters

* [Vector3](VRageMath.Vector3) value1
* [Vector3](VRageMath.Vector3) result
