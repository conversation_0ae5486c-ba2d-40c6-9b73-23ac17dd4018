← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public static bool IsZero(Vector3D value)
```

### Returns

[bool](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean?view=netframework-4.6)

### Parameters

* [Vector3D](VRageMath.Vector3D) value
### Summary

```csharp
public bool IsZero()
```

### Returns

[bool](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean?view=netframework-4.6)

### Summary

```csharp
public static bool IsZero(Vector3D value, double epsilon)
```

### Returns

[bool](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean?view=netframework-4.6)

### Parameters

* [Vector3D](VRageMath.Vector3D) value
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) epsilon
