← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3](VRageMath.Vector3)

### Summary

```csharp
public static Vector3 Reject(Vector3 vector, Vector3 direction)
```

Returns the rejection of vector from direction, i.e. projection of vector onto the plane defined by origin and direction

### Returns

[Vector3](VRageMath.Vector3)

Rejection of the vector from the given direction

### Parameters

* [Vector3](VRageMath.Vector3) vector
* [Vector3](VRageMath.Vector3) direction
### Summary

```csharp
public static void Reject(ref Vector3 vector, ref Vector3 direction, out Vector3 result)
```

Returns the rejection of vector from direction, i.e. projection of vector onto the plane defined by origin and direction

### Parameters

* [Vector3](VRageMath.Vector3) vector
* [Vector3](VRageMath.Vector3) direction
* [Vector3](VRageMath.Vector3) result
