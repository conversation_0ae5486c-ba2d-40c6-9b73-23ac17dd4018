← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3](VRageMath.Vector3)

### Summary

```csharp
public static void RotateAndScale(ref Vector3 vector, ref Matrix matrix, out Vector3 result)
```

### Parameters

* [Vector3](VRageMath.Vector3) vector
* [Matrix](VRageMath.Matrix) matrix
* [Vector3](VRageMath.Vector3) result
### Summary

```csharp
public static Vector3 RotateAndScale(Vector3 vector, Matrix matrix)
```

### Returns

[Vector3](VRageMath.Vector3)

### Parameters

* [Vector3](VRageMath.Vector3) vector
* [Matrix](VRageMath.Matrix) matrix
