← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector4](VRageMath.Vector4)

### Summary

```csharp
public static float Dot(Vector4 vector1, Vector4 vector2)
```

Calculates the dot product of two vectors.

### Returns

[float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6)

### Parameters

* [Vector4](VRageMath.Vector4) vector1
* [Vector4](VRageMath.Vector4) vector2
### Summary

```csharp
public static void Dot(ref Vector4 vector1, ref Vector4 vector2, out float result)
```

Calculates the dot product of two vectors.

### Parameters

* [Vector4](VRageMath.Vector4) vector1
* [Vector4](VRageMath.Vector4) vector2
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) result
