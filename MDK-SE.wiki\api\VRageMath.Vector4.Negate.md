← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector4](VRageMath.Vector4)

### Summary

```csharp
public static Vector4 Negate(Vector4 value)
```

Returns a vector pointing in the opposite direction.

### Returns

[Vector4](VRageMath.Vector4)

### Parameters

* [Vector4](VRageMath.Vector4) value
### Summary

```csharp
public static void Negate(ref Vector4 value, out Vector4 result)
```

Returns a vector pointing in the opposite direction.

### Parameters

* [Vector4](VRageMath.Vector4) value
* [Vector4](VRageMath.Vector4) result
