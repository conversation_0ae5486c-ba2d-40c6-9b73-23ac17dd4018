← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector4](VRageMath.Vector4)

### Summary

```csharp
public Vector4(float x, float y, float z, float w)
```

### Parameters

* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) x
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) y
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) z
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) w
### Summary

```csharp
public Vector4(Vector2 value, float z, float w)
```

### Parameters

* [Vector2](VRageMath.Vector2) value
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) z
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) w
### Summary

```csharp
public Vector4(Vector3 value, float w)
```

### Parameters

* [Vector3](VRageMath.Vector3) value
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) w
### Summary

```csharp
public Vector4(float value)
```

### Parameters

* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) value
