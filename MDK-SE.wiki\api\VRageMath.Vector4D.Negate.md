← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector4D](VRageMath.Vector4D)

### Summary

```csharp
public static Vector4D Negate(Vector4D value)
```

Returns a vector pointing in the opposite direction.

### Returns

[Vector4D](VRageMath.Vector4D)

### Parameters

* [Vector4D](VRageMath.Vector4D) value
### Summary

```csharp
public static void Negate(ref Vector4D value, out Vector4D result)
```

Returns a vector pointing in the opposite direction.

### Parameters

* [Vector4D](VRageMath.Vector4D) value
* [Vector4D](VRageMath.Vector4D) result
