← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3](VRageMath.Vector3)

### Summary

```csharp
public float Min()
```

Returns the component of the vector that is smallest of all the three components.

### Returns

[float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6)

### Summary

```csharp
public static Vector3 Min(Vector3 value1, Vector3 value2)
```

Returns a vector that contains the lowest value from each matching pair of components.

### Returns

[Vector3](VRageMath.Vector3)

### Parameters

* [Vector3](VRageMath.Vector3) value1
* [Vector3](VRageMath.Vector3) value2
### Summary

```csharp
public static void Min(ref Vector3 value1, ref Vector3 value2, out Vector3 result)
```

Returns a vector that contains the lowest value from each matching pair of components.

### Parameters

* [Vector3](VRageMath.Vector3) value1
* [Vector3](VRageMath.Vector3) value2
* [Vector3](VRageMath.Vector3) result
