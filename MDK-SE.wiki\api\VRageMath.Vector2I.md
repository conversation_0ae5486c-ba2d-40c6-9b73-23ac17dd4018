← [Index](Api-Index) ← [Namespace Index](Namespace-Index)

#### Vector2I Struct

```csharp
public struct Vector2I
```

**Namespace:** [VRageMath](VRageMath)  
**Assembly:** VRage.Math.dll

#### Fields

[static ComparerClass Comparer](VRageMath.Vector2I.Comparer)

> 

[static Vector2I One](VRageMath.Vector2I.One)

> 

[static Vector2I UnitX](VRageMath.Vector2I.UnitX)

> 

[static Vector2I UnitY](VRageMath.Vector2I.UnitY)

> 

[static Vector2I Zero](VRageMath.Vector2I.Zero)

> 

[int X](VRageMath.Vector2I.X)

> 

[int Y](VRageMath.Vector2I.Y)

> 

#### Constructors

[Vector2I(int x, int y)](VRageMath.Vector2I..ctor)

> 

[Vector2I(int width)](VRageMath.Vector2I..ctor)

> 

[Vector2I(Vector2 vec)](VRageMath.Vector2I..ctor)

> 

[Vector2I(Vector2D vec)](VRageMath.Vector2I..ctor)

> 

#### Methods

[static Vector2I Floor(Vector2 value)](VRageMath.Vector2I.Floor)

> 

[static void Max(ref Vector2I v1, ref Vector2I v2, out Vector2I max)](VRageMath.Vector2I.Max)

> 

[static Vector2I Max(Vector2I v1, Vector2I v2)](VRageMath.Vector2I.Max)

> 

[static void Min(ref Vector2I v1, ref Vector2I v2, out Vector2I min)](VRageMath.Vector2I.Min)

> 

[static Vector2I Min(Vector2I v1, Vector2I v2)](VRageMath.Vector2I.Min)

> 

[static Vector2I Round(Vector2 value)](VRageMath.Vector2I.Round)

> 

[bool Between(ref Vector2I start, ref Vector2I end)](VRageMath.Vector2I.Between)

> 

[bool Equals(object obj)](VRageMath.Vector2I.Equals)

> 

[int GetHashCode()](VRageMath.Vector2I.GetHashCode)

> 

[int Size()](VRageMath.Vector2I.Size)

> 

[string ToString()](VRageMath.Vector2I.ToString)

> 

