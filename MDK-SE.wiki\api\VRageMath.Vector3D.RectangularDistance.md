← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public static double RectangularDistance(Vector3D value1, Vector3D value2)
```

Calculates rectangular distance (a.k.a. Manhattan distance or Chessboard distace) between two vectors.

### Returns

[double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6)

### Parameters

* [Vector3D](VRageMath.Vector3D) value1
* [Vector3D](VRageMath.Vector3D) value2
### Summary

```csharp
public static double RectangularDistance(ref Vector3D value1, ref Vector3D value2)
```

Calculates rectangular distance (a.k.a. Manhattan distance or Chessboard distace) between two vectors.

### Returns

[double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6)

### Parameters

* [Vector3D](VRageMath.Vector3D) value1
* [Vector3D](VRageMath.Vector3D) value2
