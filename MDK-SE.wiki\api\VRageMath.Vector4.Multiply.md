← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector4](VRageMath.Vector4)

### Summary

```csharp
public static Vector4 Multiply(Vector4 value1, Vector4 value2)
```

Multiplies the components of two vectors by each other.

### Returns

[Vector4](VRageMath.Vector4)

### Parameters

* [Vector4](VRageMath.Vector4) value1
* [Vector4](VRageMath.Vector4) value2
### Summary

```csharp
public static void Multiply(ref Vector4 value1, ref Vector4 value2, out Vector4 result)
```

Multiplies the components of two vectors by each other.

### Parameters

* [Vector4](VRageMath.Vector4) value1
* [Vector4](VRageMath.Vector4) value2
* [Vector4](VRageMath.Vector4) result
### Summary

```csharp
public static Vector4 Multiply(Vector4 value1, float scaleFactor)
```

Multiplies a vector by a scalar.

### Returns

[Vector4](VRageMath.Vector4)

### Parameters

* [Vector4](VRageMath.Vector4) value1
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) scaleFactor
### Summary

```csharp
public static void Multiply(ref Vector4 value1, float scaleFactor, out Vector4 result)
```

Multiplies a vector by a scalar value.

### Parameters

* [Vector4](VRageMath.Vector4) value1
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) scaleFactor
* [Vector4](VRageMath.Vector4) result
