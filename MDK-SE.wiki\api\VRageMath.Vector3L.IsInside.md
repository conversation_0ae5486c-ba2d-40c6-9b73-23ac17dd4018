← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3L](VRageMath.Vector3L)

### Summary

```csharp
public bool IsInside(ref Vector3L inclusiveMin, ref Vector3L exclusiveMax)
```

### Returns

[bool](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean?view=netframework-4.6)

### Parameters

* [Vector3L](VRageMath.Vector3L) inclusiveMin
* [Vector3L](VRageMath.Vector3L) exclusiveMax
### Summary

```csharp
public bool IsInside(Vector3L inclusiveMin, Vector3L exclusiveMax)
```

### Returns

[bool](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean?view=netframework-4.6)

### Parameters

* [Vector3L](VRageMath.Vector3L) inclusiveMin
* [Vector3L](VRageMath.Vector3L) exclusiveMax
