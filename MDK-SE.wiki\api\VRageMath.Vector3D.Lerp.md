← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public static Vector3D Lerp(Vector3D value1, Vector3D value2, double amount)
```

Performs a linear interpolation between two vectors.

### Returns

[Vector3D](VRageMath.Vector3D)

### Parameters

* [Vector3D](VRageMath.Vector3D) value1
* [Vector3D](VRageMath.Vector3D) value2
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) amount
### Summary

```csharp
public static void Lerp(ref Vector3D value1, ref Vector3D value2, double amount, out Vector3D result)
```

Performs a linear interpolation between two vectors.

### Parameters

* [Vector3D](VRageMath.Vector3D) value1
* [Vector3D](VRageMath.Vector3D) value2
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) amount
* [Vector3D](VRageMath.Vector3D) result
