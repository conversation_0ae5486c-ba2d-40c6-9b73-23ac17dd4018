← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public static Vector3D Reject(Vector3D vector, Vector3D direction)
```

Returns the rejection of vector from direction, i.e. projection of vector onto the plane defined by origin and direction

### Returns

[Vector3D](VRageMath.Vector3D)

Rejection of the vector from the given direction

### Parameters

* [Vector3D](VRageMath.Vector3D) vector
* [Vector3D](VRageMath.Vector3D) direction
### Summary

```csharp
public static void Reject(ref Vector3D vector, ref Vector3D direction, out Vector3D result)
```

Returns the rejection of vector from direction, i.e. projection of vector onto the plane defined by origin and direction

### Parameters

* [Vector3D](VRageMath.Vector3D) vector
* [Vector3D](VRageMath.Vector3D) direction
* [Vector3D](VRageMath.Vector3D) result
