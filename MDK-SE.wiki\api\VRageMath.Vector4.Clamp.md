← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector4](VRageMath.Vector4)

### Summary

```csharp
public static Vector4 Clamp(Vector4 value1, Vector4 min, Vector4 max)
```

Restricts a value to be within a specified range.

### Returns

[Vector4](VRageMath.Vector4)

### Parameters

* [Vector4](VRageMath.Vector4) value1
* [Vector4](VRageMath.Vector4) min
* [Vector4](VRageMath.Vector4) max
### Summary

```csharp
public static void Clamp(ref Vector4 value1, ref Vector4 min, ref Vector4 max, out Vector4 result)
```

Restricts a value to be within a specified range.

### Parameters

* [Vector4](VRageMath.Vector4) value1
* [Vector4](VRageMath.Vector4) min
* [Vector4](VRageMath.Vector4) max
* [Vector4](VRageMath.Vector4) result
