← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public static Vector3D Clamp(Vector3D value1, Vector3D min, Vector3D max)
```

Restricts a value to be within a specified range.

### Returns

[Vector3D](VRageMath.Vector3D)

### Parameters

* [Vector3D](VRageMath.Vector3D) value1
* [Vector3D](VRageMath.Vector3D) min
* [Vector3D](VRageMath.Vector3D) max
### Summary

```csharp
public static void Clamp(ref Vector3D value1, ref Vector3D min, ref Vector3D max, out Vector3D result)
```

Restricts a value to be within a specified range.

### Parameters

* [Vector3D](VRageMath.Vector3D) value1
* [Vector3D](VRageMath.Vector3D) min
* [Vector3D](VRageMath.Vector3D) max
* [Vector3D](VRageMath.Vector3D) result
