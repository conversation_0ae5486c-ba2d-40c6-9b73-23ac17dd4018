← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector4](VRageMath.Vector4)

### Summary

```csharp
public static float DistanceSquared(Vector4 value1, Vector4 value2)
```

Calculates the distance between two vectors squared.

### Returns

[float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6)

### Parameters

* [Vector4](VRageMath.Vector4) value1
* [Vector4](VRageMath.Vector4) value2
### Summary

```csharp
public static void DistanceSquared(ref Vector4 value1, ref Vector4 value2, out float result)
```

Calculates the distance between two vectors squared.

### Parameters

* [Vector4](VRageMath.Vector4) value1
* [Vector4](VRageMath.Vector4) value2
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) result
