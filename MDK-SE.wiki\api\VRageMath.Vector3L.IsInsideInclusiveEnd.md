← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3L](VRageMath.Vector3L)

### Summary

```csharp
public bool IsInsideInclusiveEnd(ref Vector3L min, ref Vector3L max)
```

### Returns

[bool](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean?view=netframework-4.6)

### Parameters

* [Vector3L](VRageMath.Vector3L) min
* [Vector3L](VRageMath.Vector3L) max
### Summary

```csharp
public bool IsInsideInclusiveEnd(Vector3L min, Vector3L max)
```

### Returns

[bool](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean?view=netframework-4.6)

### Parameters

* [Vector3L](VRageMath.Vector3L) min
* [Vector3L](VRageMath.Vector3L) max
