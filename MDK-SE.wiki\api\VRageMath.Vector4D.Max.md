← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector4D](VRageMath.Vector4D)

### Summary

```csharp
public static Vector4 Max(Vector4 value1, Vector4 value2)
```

Returns a vector that contains the highest value from each matching pair of components.

### Returns

[Vector4](VRageMath.Vector4)

### Parameters

* [Vector4](VRageMath.Vector4) value1
* [Vector4](VRageMath.Vector4) value2
### Summary

```csharp
public static void Max(ref Vector4 value1, ref Vector4 value2, out Vector4 result)
```

Returns a vector that contains the highest value from each matching pair of components.

### Parameters

* [Vector4](VRageMath.Vector4) value1
* [Vector4](VRageMath.Vector4) value2
* [Vector4](VRageMath.Vector4) result
