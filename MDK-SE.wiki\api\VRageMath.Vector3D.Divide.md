← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public static Vector3D Divide(Vector3D value1, Vector3D value2)
```

Divides the components of a vector by the components of another vector.

### Returns

[Vector3D](VRageMath.Vector3D)

### Parameters

* [Vector3D](VRageMath.Vector3D) value1
* [Vector3D](VRageMath.Vector3D) value2
### Summary

```csharp
public static void Divide(ref Vector3D value1, ref Vector3D value2, out Vector3D result)
```

Divides the components of a vector by the components of another vector.

### Parameters

* [Vector3D](VRageMath.Vector3D) value1
* [Vector3D](VRageMath.Vector3D) value2
* [Vector3D](VRageMath.Vector3D) result
### Summary

```csharp
public static Vector3D Divide(Vector3D value1, double value2)
```

Divides a vector by a scalar value.

### Returns

[Vector3D](VRageMath.Vector3D)

### Parameters

* [Vector3D](VRageMath.Vector3D) value1
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) value2
### Summary

```csharp
public static void Divide(ref Vector3D value1, double value2, out Vector3D result)
```

Divides a vector by a scalar value.

### Parameters

* [Vector3D](VRageMath.Vector3D) value1
* [double](https://docs.microsoft.com/en-us/dotnet/api/System.Double?view=netframework-4.6) value2
* [Vector3D](VRageMath.Vector3D) result
