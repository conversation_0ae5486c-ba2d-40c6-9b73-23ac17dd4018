← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3](VRageMath.Vector3)

### Summary

```csharp
public static void GetAzimuthAndElevation(Vector3 v, out float azimuth, out float elevation)
```

### Parameters

* [Vector3](VRageMath.Vector3) v
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) azimuth
* [float](https://docs.microsoft.com/en-us/dotnet/api/System.Single?view=netframework-4.6) elevation
