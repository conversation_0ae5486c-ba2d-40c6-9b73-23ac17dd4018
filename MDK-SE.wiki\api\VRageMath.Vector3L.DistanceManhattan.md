← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3L](VRageMath.Vector3L)

### Summary

```csharp
public static long DistanceManhattan(Vector3L first, Vector3L second)
```

Manhattan distance (cube distance) X + Y + Z of Abs(first - second)

### Returns

[long](https://docs.microsoft.com/en-us/dotnet/api/System.Int64?view=netframework-4.6)

### Parameters

* [Vector3L](VRageMath.Vector3L) first
* [Vector3L](VRageMath.Vector3L) second
