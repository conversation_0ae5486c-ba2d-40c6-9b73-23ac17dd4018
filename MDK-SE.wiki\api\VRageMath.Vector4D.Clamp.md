← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector4D](VRageMath.Vector4D)

### Summary

```csharp
public static Vector4D Clamp(Vector4D value1, Vector4D min, Vector4D max)
```

Restricts a value to be within a specified range.

### Returns

[Vector4D](VRageMath.Vector4D)

### Parameters

* [Vector4D](VRageMath.Vector4D) value1
* [Vector4D](VRageMath.Vector4D) min
* [Vector4D](VRageMath.Vector4D) max
### Summary

```csharp
public static void Clamp(ref Vector4D value1, ref Vector4D min, ref Vector4D max, out Vector4D result)
```

Restricts a value to be within a specified range.

### Parameters

* [Vector4D](VRageMath.Vector4D) value1
* [Vector4D](VRageMath.Vector4D) min
* [Vector4D](VRageMath.Vector4D) max
* [Vector4D](VRageMath.Vector4D) result
