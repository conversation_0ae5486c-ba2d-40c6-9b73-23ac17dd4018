← [Index](Api-Index) ← [Namespace Index](Namespace-Index)

#### EqualityComparer Class

```csharp
public class EqualityComparer: IEqualityComparer<Vector4I>, IComparer<Vector4I>
```

**Namespace:** [VRageMath](VRageMath)  
**Assembly:** VRage.Math.dll

**Implements:**  
* [IComparer&lt;Vector4I&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IComparer-1?view=netframework-4.6)  
* [IEqualityComparer&lt;Vector4I&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEqualityComparer-1?view=netframework-4.6)

