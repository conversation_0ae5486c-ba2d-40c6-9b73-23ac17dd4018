← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector3D](VRageMath.Vector3D)

### Summary

```csharp
public static Vector3D ProjectOnPlane(ref Vector3D vec, ref Vector3D planeNormal)
```

Projects given vector on plane specified by it's normal.

### Returns

[Vector3D](VRageMath.Vector3D)

Vector projected on plane

### Parameters

* [Vector3D](VRageMath.Vector3D) vec
* [Vector3D](VRageMath.Vector3D) planeNormal
