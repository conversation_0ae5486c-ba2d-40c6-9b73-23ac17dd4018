← [Index](Api-Index) ← [Namespace Index](Namespace-Index)

# VRageMath

**[Base27Directions](VRageMath.Base27Directions)**  
**[Base6Directions](VRageMath.Base6Directions)**  
**[BoundingBox](VRageMath.BoundingBox)**  
**[BoundingBox2](VRageMath.BoundingBox2)**  
**[BoundingBox2D](VRageMath.BoundingBox2D)**  
**[BoundingBox2I](VRageMath.BoundingBox2I)**  
**[BoundingBoxD](VRageMath.BoundingBoxD)**  
**[BoundingBoxExtensions](VRageMath.BoundingBoxExtensions)**  
**[BoundingBoxI](VRageMath.BoundingBoxI)**  
**[BoundingFrustum](VRageMath.BoundingFrustum)**  
**[BoundingFrustumD](VRageMath.BoundingFrustumD)**  
**[BoundingFrustumExtensions](VRageMath.BoundingFrustumExtensions)**  
**[BoundingSphere](VRageMath.BoundingSphere)**  
**[BoundingSphereD](VRageMath.BoundingSphereD)**  
**[BoxCornerEnumerator](VRageMath.BoxCornerEnumerator)**  
**[Capsule](VRageMath.Capsule)**  
**[CapsuleD](VRageMath.CapsuleD)**  
**[Color](VRageMath.Color)**  
**[ColorExtensions](VRageMath.ColorExtensions)**  
**[CompressedPositionOrientation](VRageMath.CompressedPositionOrientation)**  
**[ContainmentType](VRageMath.ContainmentType)**  
**[CubeFace](VRageMath.CubeFace)**  
**[Curve](VRageMath.Curve)**  
**[CurveContinuity](VRageMath.CurveContinuity)**  
**[CurveKey](VRageMath.CurveKey)**  
**[CurveKeyCollection](VRageMath.CurveKeyCollection)**  
**[CurveLoopType](VRageMath.CurveLoopType)**  
**[CurveTangent](VRageMath.CurveTangent)**  
**[HyperSphereHelpers](VRageMath.HyperSphereHelpers)**  
**[IAddOp](VRageMath.IAddOp`1)**  
**[Line](VRageMath.Line)**  
**[LineD](VRageMath.LineD)**  
**[MathHelper](VRageMath.MathHelper)**  
**[MathHelperD](VRageMath.MathHelperD)**  
**[Matrix](VRageMath.Matrix)**  
**[Matrix3x3](VRageMath.Matrix3x3)**  
**[MatrixD](VRageMath.MatrixD)**  
**[MatrixI](VRageMath.MatrixI)**  
**[MyBlockOrientation](VRageMath.MyBlockOrientation)**  
**[MyBounds](VRageMath.MyBounds)**  
**[MyCuboid](VRageMath.MyCuboid)**  
**[MyCuboidSide](VRageMath.MyCuboidSide)**  
**[MyDynamicAABBTree](VRageMath.MyDynamicAABBTree)**  
**[MyDynamicAABBTreeD](VRageMath.MyDynamicAABBTreeD)**  
**[MyLineSegmentOverlapResult](VRageMath.MyLineSegmentOverlapResult`1)**  
**[MyMath](VRageMath.MyMath)**  
**[MyMortonCode3D](VRageMath.MyMortonCode3D)**  
**[MyMovingAverage](VRageMath.MyMovingAverage)**  
**[MyOrientedBoundingBox](VRageMath.MyOrientedBoundingBox)**  
**[MyOrientedBoundingBoxD](VRageMath.MyOrientedBoundingBoxD)**  
**[MyQuad](VRageMath.MyQuad)**  
**[MyQuadD](VRageMath.MyQuadD)**  
**[MyShort4](VRageMath.MyShort4)**  
**[MyTransform](VRageMath.MyTransform)**  
**[MyTransformD](VRageMath.MyTransformD)**  
**[MyUShort4](VRageMath.MyUShort4)**  
**[NullableVector3DExtensions](VRageMath.NullableVector3DExtensions)**  
**[NullableVector3Extensions](VRageMath.NullableVector3Extensions)**  
**[Plane](VRageMath.Plane)**  
**[PlaneD](VRageMath.PlaneD)**  
**[PlaneIntersectionType](VRageMath.PlaneIntersectionType)**  
**[Point](VRageMath.Point)**  
**[Quaternion](VRageMath.Quaternion)**  
**[QuaternionD](VRageMath.QuaternionD)**  
**[Ray](VRageMath.Ray)**  
**[RayD](VRageMath.RayD)**  
**[Rectangle](VRageMath.Rectangle)**  
**[RectangleF](VRageMath.RectangleF)**  
**[SerializableRange](VRageMath.SerializableRange)**  
**[SymmetricSerializableRange](VRageMath.SymmetricSerializableRange)**  
**[Vector2](VRageMath.Vector2)**  
**[Vector2B](VRageMath.Vector2B)**  
**[Vector2D](VRageMath.Vector2D)**  
**[Vector2I](VRageMath.Vector2I)**  
**[Vector3](VRageMath.Vector3)**  
**[Vector3B](VRageMath.Vector3B)**  
**[Vector3D](VRageMath.Vector3D)**  
**[Vector3Extensions](VRageMath.Vector3Extensions)**  
**[Vector3I](VRageMath.Vector3I)**  
**[Vector3I_RangeIterator](VRageMath.Vector3I_RangeIterator)**  
**[Vector3INormalEqualityComparer](VRageMath.Vector3INormalEqualityComparer)**  
**[Vector3L](VRageMath.Vector3L)**  
**[Vector3L_RangeIterator](VRageMath.Vector3L_RangeIterator)**  
**[Vector3LNormalEqualityComparer](VRageMath.Vector3LNormalEqualityComparer)**  
**[Vector3S](VRageMath.Vector3S)**  
**[Vector3UByte](VRageMath.Vector3UByte)**  
**[Vector3Ushort](VRageMath.Vector3Ushort)**  
**[Vector4](VRageMath.Vector4)**  
**[Vector4D](VRageMath.Vector4D)**  
**[Vector4I](VRageMath.Vector4I)**  
**[Vector4UByte](VRageMath.Vector4UByte)**

