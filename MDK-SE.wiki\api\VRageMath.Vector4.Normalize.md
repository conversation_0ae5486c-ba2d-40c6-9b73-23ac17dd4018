← [Index](Api-Index) ← [Namespace Index](Namespace-Index) ← [Vector4](VRageMath.Vector4)

### Summary

```csharp
public void Normalize()
```

Turns the current vector into a unit vector.

### Summary

```csharp
public static Vector4 Normalize(Vector4 vector)
```

Creates a unit vector from the specified vector.

### Returns

[Vector4](VRageMath.Vector4)

### Parameters

* [Vector4](VRageMath.Vector4) vector
### Summary

```csharp
public static void Normalize(ref Vector4 vector, out Vector4 result)
```

Returns a normalized version of the specified vector.

### Parameters

* [Vector4](VRageMath.Vector4) vector
* [Vector4](VRageMath.Vector4) result
